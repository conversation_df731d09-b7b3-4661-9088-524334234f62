/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Dimensions,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {
  navigateBack,
  RootScreen,
} from '../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import {useEffect, useRef, useState} from 'react';
import {RadioButton} from 'react-native-paper';
import CountdownTimer from '../components/countDownTimer';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {useExamData} from '../../../redux/hook/examHook';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ExamActions} from '../../../redux/reducers/examReducer';
import {randomGID} from '../../../utils/Utils';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {ExamType, StatusExam, StorageContanst} from '../../../Config/Contanst';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import {DataController} from '../../../base/baseController';

export default function DoingTestNew() {
  const {t} = useTranslation();
  const dateStart = new Date().getTime();
  const route = useRoute<any>();
  const {testId} = route.params;
  const navigation = useNavigation<any>();

  // State management
  const [testData, setTestData] = useState<any>();
  const [sections, setSections] = useState<any[]>([]);
  const [exams, setExams] = useState<any[]>([]);
  const [currentQuestions, setCurrentQuestions] = useState<any[]>([]);
  const [currentAnswers, setCurrentAnswers] = useState<any[]>([]);
  const [selectedSection, setSelectedSection] = useState<any>(null);
  const [selectedExam, setSelectedExam] = useState<any>(null);
  const [currentExamIndex, setCurrentExamIndex] = useState(0);
  const [loading, setLoading] = useState(true);

  // Controllers
  const examController = new DataController('Exam');
  const answerController = new DataController('Answer');
  const testController = new DataController('Test');
  const sectionController = new DataController('Section');
  useEffect(() => {
    const getTestData = async () => {
      if (testId) {
        const res = await examController.getPatternList({
          query: `@TestId:{${testId}} @Status:[2]`,
          pattern: {
            TestId: ['Id', 'Name', 'Time', 'Level', 'SectionId'],
            SectionId: ['Id', 'Name', 'Time'],
            QuestionId: [
              'Id',
              'Name',
              'SelectionType',
              'Score',
              'Audio',
              'Img',
            ],
          },
        });
        if (res.code === 200) {
          setTestData(res.Test[0]);
          setSections(
            res.Test[0].SectionId.split(',').map((id: any) =>
              res.Section.find((e: any) => e.Id === id),
            ),
          );
          setExams(res.data.sort((a: any, b: any) => a.Sort - b.Sort));
          setQuestions(res.Question);
        }
      }
    };
    getTestData();
  }, [testId]);
  useEffect(() => {
    if (questions.length) {
      answerController
        .getListSimple({
          query: `@QuestionId:{${questions.map((e: any) => e.Id).join(' | ')}}`,
          returns: ['Id', 'Name', 'Content', 'Sort', 'QuestionId'],
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        })
        .then(res => {
          if (res.code === 200) {
            setAnswers(res.data);
          }
        });
    }
  }, [questions.length]);

  const next = () => {
    // if (type === ExamType.quiz) {
    //   const percent = Math.min(
    //     (currentPage + 1 / listQuestion.length) * 100,
    //     100,
    //   );
    //   if (percent > maxProgressRef.current) {
    //     dispatch(
    //       LessonActions.updateProcess({
    //         Id: Step.lessonId,
    //         CourseId: Step.courseId,
    //         stepId: Step.stepId,
    //         stepOrder: Step.stepOrder,
    //         PercentCompleted: percent,
    //         isLastStep: Step.isLastStep,
    //         lessonIndex: Step.lessonIndex,
    //         courseCustomerId: Step.courseCustomerId,
    //         Type: 'Quiz',
    //       }),
    //     );
    //     maxProgressRef.current = percent;
    //   }
    // }
    // if (type === ExamType.Real) {
    //   const percent = Math.min((currentPage / listQuestion.length) * 100, 100);
    //   if (percent > maxProgressRef.current) {
    //     dispatch(
    //       LessonActions.updateProcess({
    //         Id: Step.lessonId,
    //         CourseId: Step.courseId,
    //         stepId: Step.stepId,
    //         stepOrder: Step.stepOrder,
    //         PercentCompleted: percent,
    //         isLastStep: Step.isLastStep,
    //         lessonIndex: Step.lessonIndex,
    //         courseCustomerId: Step.courseCustomerId,
    //         Type: 'Exam',
    //       }),
    //     );
    //     maxProgressRef.current = percent;
    //   }
    // }
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage + 1); // Navigate to the next page
      setCurrentPage(currentPage + 1);
    }
  };

  const previous = () => {
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage - 1); // Navigate to the previous page
      setCurrentPage(currentPage - 1);
    }
  };
  const onSubmit = async (dateEnd: number, dateStart: number, id: string) => {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    var score = 0;
    var totalS = 0;
    var lstDetail: any[] = [];
    var lstDetailAnswer: any[] = [];
    var idTest = randomGID();
    var correctAnswer = 0;
    listQuestion?.map((item, index) => {
      totalS += item.Score;
      var isResultDetail = false;
      var IdAnswer;
      var idsAnser = item.lstAnswer.filter((tp: any) => tp.choose === true);

      if (item.SelectionType === 1) {
        // tính điểm với câu hỏi chọn 1
        IdAnswer = idsAnser[0]?.Id;
        if (
          item.lstAnswer.some(
            (a: any) => a.choose === true && a.IsResult === true,
          )
        ) {
          score += item.Score ?? 1;
          correctAnswer++;
          isResultDetail = true;
        }
      } else {
        // tính điểm với câu hỏi chọn nhiều - Lấy hết đáp án cần phải tích đúng. kiểm tra xem user chọn hết các đáp án đấy k
        if (idsAnser?.length > 0) {
          IdAnswer = idsAnser?.map((u: any) => u.Id).join(',');
        }
        var wrongChoices = item.lstAnswer.filter(
          (i: any) => i.IsResult !== true && i.choose === true,
        );
        var correctAnswers = item.lstAnswer.filter(
          (i: any) => i.IsResult === true,
        );
        if (
          !correctAnswers.some((a: any) => a.choose !== true) &&
          wrongChoices.length === 0
        ) {
          score += item.Score ?? 1;
          correctAnswer++;
          isResultDetail = true;
        }
      }
      // lưu lịch sử các câu hỏi/
      var IdDetail = randomGID();
      lstDetail.push({
        Id: IdDetail,
        QuestionId: item.Id,
        Sort: index + 1,
        AnswerId: IdAnswer,
        TestId: idTest,
        SelectionType: item.SelectionType,
        IsResult: isResultDetail,
        DateCreated: new Date().getTime(),
        QuestionContent: item.Content,
      });
      // lưu lịch sử các đáp án trên từng câu hỏi/
      item.lstAnswer.map((answer: any) => {
        lstDetailAnswer.push({
          Id: randomGID(),
          Name: answer.Content,
          DateCreated: new Date().getTime(),
          IsResult: answer.IsResult ?? false,
          QuestionId: item.Id,
          Test_ResultId: IdDetail,
          AnswerId: answer.Id,
        });
      });
    });
    const data = {
      Id: idTest,
      DateCreated: new Date().getTime(),
      DateEnd: dateEnd,
      DateStart: dateStart,
      Type: examInfor.Type,
      ExamId: id,
      Name: examInfor.Name,
      CustomerId: cusId,
      Time: examInfor.Time,
      Score: score ?? 0,
      Status:
        (score / totalS) * 100 >= examInfor.PassingScore
          ? StatusExam.passed
          : StatusExam.fail,
      TotalAnswerCorrect: correctAnswer,
      TotalQuestion: listQuestion.length ?? 0,
    };
    if (
      (score / totalS) * 100 >= examInfor.PassingScore &&
      examInfor.Type === ExamType.Real
    ) {
      await dispatch(
        LessonActions.updateProcess({
          Id: Step.lessonId,
          CourseId: Step.courseId,
          stepId: Step.stepId,
          stepOrder: Step.stepOrder,
          PercentCompleted: 100,
          isLastStep: Step.isLastStep,
          lessonIndex: Step.lessonIndex,
          courseCustomerId: Step.courseCustomerId,
          Type: 'Exam',
        }),
      );
    }
    await dispatch(ExamActions.examSubmit(data, lstDetail, lstDetailAnswer));
  };
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute<any>();
  const {id, type, Step} = route.params;
  const {examInfor} = useExamData();
  const {loading} = useExamData();
  const {listQuestion} = useExamData();
  useEffect(() => {
    maxProgressRef.current = 0;
    setCurrentPage(0);
  }, [Step?.stepId]);
  const bottomSheetRef = useRef<any>(null);
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        action={
          <AppButton
            title={t('exam.submit')}
            containerStyle={{
              justifyContent: 'flex-start',
              alignSelf: 'baseline',
              paddingRight: 16,
            }}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                title: t('exam.confirmSubmit'),
                prefixAction: <View />,
                suffixAction: (
                  <TouchableOpacity
                    onPress={() => hideBottomSheet(bottomSheetRef)}
                    style={{padding: 6, alignItems: 'center'}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={20}
                      color={ColorThemes.light.neutral_text_body_color}
                    />
                  </TouchableOpacity>
                ),
                children: (
                  <ConFirmEndTest
                    ref={bottomSheetRef}
                    dateStart={dateStart}
                    dateEnd={dateStart + examInfor?.Time * 60 * 1000}
                    id={examInfor?.Id}
                    courseId={id}
                    type={type}
                    step={Step}
                  />
                ),
              });
            }}
            textColor={ColorThemes.light.infor_main_color}
          />
        }
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <CountdownTimer
              textStyle={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.neutral_text_title_color,
              }}
              initialMinutes={examInfor?.Time ?? 0}
              onTimeUp={() => {
                onSubmit(
                  dateStart + examInfor.Time * 60 * 1000,
                  dateStart,
                  examInfor.Id,
                );
              }}
            />
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              {`${currentPage + 1}/${listQuestion?.length ?? 0}`}
            </Text>
          </View>
        }
        backIcon={<Winicon src="outline/user interface/e-remove" size={20} />}
        onBack={() => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            title: t('exam.confirmExit'),
            prefixAction: <View />,
            suffixAction: (
              <TouchableOpacity
                onPress={() => hideBottomSheet(bottomSheetRef)}
                style={{padding: 6, alignItems: 'center'}}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.neutral_text_body_color}
                />
              </TouchableOpacity>
            ),
            children: <ConFirmCancel ref={bottomSheetRef} />,
          });
        }}
      />
      {/* checklist */}
      <AppButton
        containerStyle={{
          width: 60,
          height: 60,
          borderRadius: 30,
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          zIndex: 1,
          bottom: 90,
          right: 16,
        }}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        title={
          <Winicon
            src="outline/text/ordered-list"
            size={24}
            color={ColorThemes.light.neutral_absolute_background_color}
          />
        }
        onPress={async () => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            title: t('exam.selectQuestion'),
            prefixAction: <View />,
            suffixAction: (
              <TouchableOpacity
                onPress={() => hideBottomSheet(bottomSheetRef)}
                style={{padding: 6, alignItems: 'center'}}>
                <Winicon
                  src="outline/layout/xmark"
                  size={listQuestion?.length ?? 1}
                />
              </TouchableOpacity>
            ),
            children: (
              <Pressable
                style={{
                  height: Dimensions.get('screen').height / 2,
                  width: '100%',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: 16,
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  backgroundColor:
                    ColorThemes.light.neutral_absolute_background_color,
                }}>
                {listQuestion?.map((item, index) => (
                  <AppButton
                    key={item.Id}
                    containerStyle={{
                      width: 32,
                      height: 32,
                      borderRadius: 30,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    backgroundColor={
                      item.lstAnswer.some((a: any) => a.choose)
                        ? ColorThemes.light.success_background
                        : ColorThemes.light.neutral_main_background_color
                    }
                    onPress={() => {
                      if (pagerRef.current) {
                        pagerRef.current.setPage(index); // Navigate to the page
                        setCurrentPage(index);
                        hideBottomSheet(bottomSheetRef);
                      }
                    }}
                    borderColor="transparent"
                    title={`${index + 1}`}
                    textColor={
                      item.lstAnswer.some((a: any) => a.choose)
                        ? ColorThemes.light.success_main_color
                        : ColorThemes.light.neutral_text_body_color
                    }
                  />
                ))}
              </Pressable>
            ),
          });
        }}
      />
      {/* content */}
      {loading ? (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <ActivityIndicator color={ColorThemes.light.primary_main_color} />
        </View>
      ) : (
        <ContentTest ref={pagerRef} />
      )}

      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          alignContent: 'center',
          paddingHorizontal: 16,
        }}>
        {currentPage === 0 ? (
          <View />
        ) : (
          <AppButton
            title={t('exam.previousQuestion')}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            prefixIconSize={16}
            prefixIcon={'outline/arrows/left-arrow'}
            onPress={previous} // Call previous function
            textColor={ColorThemes.light.infor_main_color}
          />
        )}
        {currentPage === listQuestion.length - 1 ? (
          <View />
        ) : (
          <AppButton
            title={t('exam.nextQuestion')}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            suffixIconSize={16}
            suffixIcon={'outline/arrows/right-arrow'}
            onPress={async () => {
              next();
            }} // Call next function
            textColor={ColorThemes.light.infor_main_color}
          />
        )}
      </WScreenFooter>
    </SafeAreaView>
  );
}

const ContentTest = ({ref}: any) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute<any>();
  const {id} = route.params;
  const {listQuestion} = useExamData();
  return (
    <View style={{flex: 1}}>
      <PagerView
        ref={ref}
        style={{height: '100%'}}
        initialPage={0}
        scrollEnabled={false}>
        {listQuestion?.map((item: any, index) => (
          <View key={index}>
            {/* content */}
            <ScrollView style={{flex: 1}}>
              {/* question */}
              <ListTile
                style={{paddingHorizontal: 16, padding: 0, paddingTop: 16}}
                title={
                  <RenderHTML
                    contentWidth={Dimensions.get('window').width}
                    source={{html: item.Content}}
                    tagsStyles={{
                      body: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                      },
                      div: {
                        color: '#313135',
                        fontSize: 14,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                      },
                      p: {
                        color: '#313135',
                        fontSize: 16,
                        lineHeight: 20,
                        fontFamily: 'Inter',
                      },
                      b: {
                        fontWeight: 'bold',
                      },
                      i: {
                        fontStyle: 'italic',
                      },
                      u: {
                        textDecorationLine: 'underline',
                      },
                      img: {
                        marginVertical: 10,
                        marginHorizontal: 10,
                        alignSelf: 'center',
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        maxWidth: '100%',
                        height: 300,
                        objectFit: 'fill',
                      },
                    }}
                  />
                }
                titleStyle={{
                  ...TypoSkin.title3,
                  fontWeight: '700',
                  color: ColorThemes.light.neutral_text_title_color,
                }}
              />
              <ListTile
                style={{
                  paddingHorizontal: 16,
                  padding: 0,
                  paddingBottom: 16,
                  paddingTop: 8,
                }}
                title={
                  item.SelectionType === 2
                    ? t('exam.selectMultipleAnswers')
                    : t('exam.selectOneAnswer')
                }
                titleStyle={{
                  ...TypoSkin.body2,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}
              />
              {/* answers */}
              <View style={{gap: 8, paddingBottom: 150}}>
                {item.lstAnswer?.map((answer: any, index: number) => {
                  return (
                    <ListTile
                      key={index}
                      onPress={() => {
                        dispatch(ExamActions.choose(item.Id, answer.Id));
                      }}
                      leading={
                        item.SelectionType === 2 ? (
                          <Checkbox
                            key={index}
                            value={answer.choose ?? false}
                            onChange={() => {
                              dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                            checkboxStyle={{
                              backgroundColor:
                                ColorThemes.light.infor_main_color,
                            }}
                          />
                        ) : (
                          <RadioButton.Android
                            key={index}
                            value={answer.Id}
                            status={answer.choose ? 'checked' : 'unchecked'}
                            color={ColorThemes.light.infor_main_color}
                            onPress={() => {
                              dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                          />
                        )
                      }
                      listtileStyle={{gap: 12, padding: 8}}
                      title={`${answer.Content ?? ''}`}
                      style={{
                        borderColor: answer.choose
                          ? ColorThemes.light.infor_main_color
                          : ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                        marginHorizontal: 16,
                        padding: 0,
                      }}
                      titleStyle={{
                        ...TypoSkin.body2,
                        color: ColorThemes.light.neutral_text_subtitle_color,
                      }}
                    />
                  );
                })}
              </View>
            </ScrollView>
          </View>
        ))}
      </PagerView>
    </View>
  );
};

const ConFirmEndTest = (pros: any) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const {listQuestion, examInfor} = useExamData();
  const navigation = useNavigation<any>();
  useEffect(() => {}, []);
  return (
    <View
      style={{
        height: 150,
        width: '100%',
        gap: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingHorizontal: 16,
      }}>
      <AppButton
        title={t('exam.submitNow')}
        backgroundColor={ColorThemes.light.primary_main_color}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          marginTop: 16,
          borderRadius: 8,
        }}
        onPress={async () => {
          const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
          var score = 0;
          var totalS = 0;
          var lstDetail: any[] = [];
          var lstDetailAnswer: any[] = [];
          var idTest = randomGID();
          var correctAnswer = 0;
          //check xem người dùng có trả lời câu nào ko
          const listCheck = listQuestion.filter((item: any) =>
            item.lstAnswer.some((a: any) => a.choose === true),
          );

          if (listCheck.length === 0) {
            navigation.replace(RootScreen.resultTest, {
              id: pros?.id,
              courseId: pros.step?.courseId,
              type: pros?.type,
              isCheck: true,
            });
          } else {
            listQuestion?.map((item, index) => {
              totalS += item.Score;
              var isResultDetail = false;
              var IdAnswer;
              var idsAnser = item.lstAnswer.filter(
                (tp: any) => tp.choose === true,
              );

              if (item.SelectionType === 1) {
                // tính điểm với câu hỏi chọn 1
                IdAnswer = idsAnser[0]?.Id;
                if (
                  item.lstAnswer.some(
                    (a: any) => a.choose === true && a.IsResult === true,
                  )
                ) {
                  score += item.Score ?? 1;
                  correctAnswer++;
                  isResultDetail = true;
                }
              } else {
                // tính điểm với câu hỏi chọn nhiều - Lấy hết đáp án cần phải tích đúng. kiểm tra xem user chọn hết các đáp án đấy k
                if (idsAnser?.length > 0) {
                  IdAnswer = idsAnser?.map((u: any) => u.Id).join(',');
                }
                var wrongChoices = item.lstAnswer.filter(
                  (i: any) => i.IsResult !== true && i.choose === true,
                );
                var correctAnswers = item.lstAnswer.filter(
                  (i: any) => i.IsResult === true,
                );

                if (
                  !correctAnswers.some((a: any) => a.choose !== true) &&
                  wrongChoices.length === 0
                ) {
                  score += item.Score ?? 1;
                  correctAnswer++;
                  isResultDetail = true;
                }
              }
              // lưu lịch sử các câu hỏi/
              var IdDetail = randomGID();
              lstDetail.push({
                Id: IdDetail,
                QuestionId: item.Id,
                Sort: index + 1,
                AnswerId: IdAnswer,
                SelectionType: item.SelectionType,
                TestId: idTest,
                IsResult: isResultDetail,
                DateCreated: new Date().getTime(),
                QuestionContent: item.Content,
              });
              // lưu lịch sử các đáp án trên từng câu hỏi/
              item.lstAnswer.map((answer: any) => {
                lstDetailAnswer.push({
                  Id: randomGID(),
                  Name: answer.Content,
                  DateCreated: new Date().getTime(),
                  IsResult: answer.IsResult ?? false,
                  QuestionId: item.Id,
                  Test_ResultId: IdDetail,
                  AnswerId: answer.Id,
                });
              });
            });
            const data = {
              Id: idTest,
              DateCreated: new Date().getTime(),
              DateEnd: pros.dateEnd,
              DateStart: pros.dateStart,
              ExamId: pros.id,
              Type: examInfor.Type,
              Name: examInfor.Name,
              CustomerId: cusId,
              Time: examInfor.Time,
              Score: score ?? 0,
              Status:
                (score / totalS) * 100 >= examInfor.PassingScore
                  ? StatusExam.passed
                  : StatusExam.fail,
              TotalAnswerCorrect: correctAnswer,
              TotalQuestion: listQuestion.length ?? 0,
            };

            if (
              (score / totalS) * 100 >= examInfor.PassingScore &&
              examInfor.Type !== ExamType.Try
            ) {
              dispatch(
                LessonActions.updateProcess({
                  Id: pros.step.lessonId,
                  CourseId: pros.step.courseId,
                  stepId: pros.step.stepId,
                  stepOrder: pros.step.stepOrder,
                  PercentCompleted: 100,
                  isLastStep: pros.step.isLastStep,
                  lessonIndex: pros.step.lessonIndex,
                  courseCustomerId: pros.step.courseCustomerId,
                  Type: examInfor.Type === ExamType.Real ? 'Exam' : 'Quiz',
                }),
              );
            }
            hideBottomSheet(pros.ref);

            await dispatch(
              ExamActions.examSubmit(data, lstDetail, lstDetailAnswer),
            ).then(() => {
              navigation.replace(RootScreen.resultTest, {
                id: pros?.id,
                courseId: pros.step?.courseId,
                type: pros?.type,
              });
            });
          }
        }}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
      <AppButton
        title={t('exam.continueExam')}
        backgroundColor={ColorThemes.light.neutral_main_background_color}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          borderRadius: 8,
        }}
        onPress={() => {
          hideBottomSheet(pros.ref);
        }}
        textColor={ColorThemes.light.neutral_text_body_color}
      />
    </View>
  );
};
const ConFirmCancel = ({ref}: {ref: any}) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  return (
    <View
      style={{
        height: 180,
        width: '100%',
        gap: 16,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        paddingHorizontal: 16,
      }}>
      <Text
        style={{
          ...TypoSkin.body3,
          color: ColorThemes.light.neutral_text_subtitle_color,
          textAlign: 'center',
        }}>
        {t('exam.exitWarning')}
      </Text>
      <AppButton
        title={t('exam.exit')}
        backgroundColor={ColorThemes.light.error_main_color}
        borderColor="transparent"
        containerStyle={{
          height: 45,

          borderRadius: 8,
        }}
        onPress={() => {
          dispatch(ExamActions.resetReducer());
          hideBottomSheet(ref);
          navigateBack();
        }}
        textColor={ColorThemes.light.neutral_absolute_background_color}
      />
      <AppButton
        title={t('exam.continueExam')}
        backgroundColor={ColorThemes.light.neutral_main_background_color}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          borderRadius: 8,
        }}
        onPress={() => {
          hideBottomSheet(ref);
        }}
        textColor={ColorThemes.light.neutral_text_body_color}
      />
    </View>
  );
};
