import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {CourseDA} from '../../modules/Course/da';
import {examDA} from '../../modules/exam/da';
import {randomGID} from '../../utils/Utils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
export const FETCH_PROCCESS_LESSON_DATA = 'FETCH_PROCCESS_LESSON_DATA';
export const UPDATE_PROCESS = 'UPDATE_PROCESS';
export const SET_CURRENTSTEP = 'SET_CURRENTSTEP';
const initialState: {
  lessons: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  lessons: [],
  loading: true,
  success: false,
  error: null,
};
export const proccessLessonSlice = createSlice({
  name: 'proccessLesson',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_PROCCESS_LESSON_DATA:
          state.lessons = action.payload.lessons;
          break;
        case UPDATE_PROCESS:
          const payload = action.payload.lessons;
          // Validate payload
          if (!payload || !payload.Id) {
            console.error('Invalid payload for UPDATE_PROCESS:', payload);
            break;
          }

          var updateLesson = state.lessons.map((item: any) => {
            // Normalize ID comparison (convert to string to be safe)
            if (String(item.id) === String(payload.Id)) {
              const updatedSteps = item.steps.map((step: any) => {
                // Update only the specific step
                if (step.order === payload.stepOrder) {
                  console.log('Updating step:', {
                    lessonId: item.id,
                    stepOrder: step.order,
                    oldPercent: step.PercentCompleted,
                    newPercent: payload.PercentCompleted,
                  });

                  return {
                    ...step,
                    PercentCompleted:
                      payload.PercentCompleted ?? step.PercentCompleted,
                  };
                }
                return step;
              });

              return {
                ...item,
                steps: updatedSteps,
              };
            }
            // Don't modify other lessons
            return item;
          });

          state.lessons = updateLesson;
          break;
        case SET_CURRENTSTEP:
          return {
            ...state,
            lessons: state.lessons.map(lesson => ({
              ...lesson,
              steps: lesson.steps.map((step: any) => ({
                ...step,
                IsCurrentStep:
                  lesson.id === action.payload.lessons.lessonId &&
                  step.order === action.payload.lessons.stepOrder
                    ? true
                    : false,
              })),
            })),
          };
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
      state.lessons = [];
    },
  },
});
export default proccessLessonSlice.reducer;
const {handleActions, onFetching} = proccessLessonSlice.actions;
const courseDA = new CourseDA();
const examda = new examDA();
export class LessonActions {
  static getProccessLesson = (id: string) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const response = await courseDA.getLessonbyCourseId(id);
    if (response) {
      const lessonIds = response.data.map((item: any) => item.Id);
      const proccess = await courseDA.getProccessListLesson(id, lessonIds);

      const list = await Promise.all(
        response.data.map(async (item: any) => {
          const steps: {
            type: string;
            displayName: string;
            order: number;
            id?: number;
            PercentCompleted: number;
            videoIds?: string[];
          }[] = [];
          var order = 0;
          if (item.Introduction) {
            order = order + 1;
            steps.push({
              type: 'Intro',
              displayName: 'Bài giới thiệu',
              order: order,
              PercentCompleted: proccess?.[0].IntrolPercent ?? 0,
            });
          }
          if (item.Video) {
            order = order + 1;
            const videoIds = item.Video.split(',');
            debugger
            var percent = 0;
            if (videoIds.length <= 1) {
              percent = proccess?.[0].DocumentPercent ?? 0;
            } else {
              percent = proccess?.[0].DocumentStep ?
                (proccess?.[0].DocumentStep?.split(',').length /
                  videoIds.length) *
                100 : 0;
              percent = percent > 100 ? 100 : percent;
            }
            steps.push({
              type: 'Video',
              displayName: 'Nội dung bài học',
              order: order,
              PercentCompleted: percent,
              videoIds: videoIds,
            });
          }
          // if (item.Document) {
          //   order = order + 1;
          //   steps.push({
          //     type: 'Document',
          //     displayName: 'Tài liệu',
          //     order: order,
          //     PercentCompleted:
          //       proccess?.find((test: any) => test.currentStep === order)
          //         ?.PercentCompleted ?? 0,
          //   });
          // }
          //check xem có bài quiz theo lesson ko
          const quiz = await examda.getListQuizbyLessonId(item.Id);
          if (quiz?.data?.length > 0) {
            order = order + 1;
            steps.push({
              type: 'Quiz',
              displayName: 'Bài ôn tập',
              order: order,
              id: quiz?.data[0].Id,
              PercentCompleted: proccess?.[0].QuizPercent ?? 0,
            });
          }
          //check xem có bài exam theo lesson ko
          const exam = await examda.getListExambyLessonId(item.Id);
          if (exam?.data?.length > 0) {
            order = order + 1;
            steps.push({
              type: 'Exam',
              displayName: 'Bài kiểm tra',
              order: order,
              id: exam?.data[0].Id,
              PercentCompleted: proccess?.[0].ExamPercent ?? 0,
            });
          }
          return {
            id: item.Id,
            title: item.Name,
            steps: steps,
          };
        }),
      );
      dispatch(
        handleActions({
          type: FETCH_PROCCESS_LESSON_DATA,
          lessons: list,
        }),
      );
    }
  };
  static updateProcess = (lesson: any) => async (dispatch: Dispatch) => {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (lesson.isLastStep && lesson.PercentCompleted === 100) {
      const courseCustomer = await courseDA.getCourseCustomerItem(
        lesson.courseCustomerId,
      );
      if (courseCustomer && courseCustomer.Process < lesson.lessonIndex + 1) {
        const dataUpdate = {
          ...courseCustomer,
          Process: (courseCustomer.Process ?? 0) + 1,
        };
        await courseDA.updateCourseCustomer(dataUpdate);
      }
    }
    const dataExist = await courseDA.checkExistsProccessLesson(
      lesson.CourseId,
      lesson.Id,
    );
    if (dataExist) {
      var dataExistUpdate = {};
      switch (lesson.Type) {
        case 'Intro':
          if (
            dataExist.IntrolPercent < lesson.PercentCompleted &&
            dataExist.IntrolPercent < 100
          ) {
            dataExistUpdate = {
              ...dataExist,
              IntrolPercent: lesson?.PercentCompleted ?? 0,
              IntrolStep: lesson?.stepOrder + '',
            };
            const response = await courseDA.updateProccessLesson(
              dataExistUpdate,
            );
            if (response) {
              dispatch(
                handleActions({
                  type: UPDATE_PROCESS,
                  lessons: lesson,
                }),
              );
            }
          }
          break;
        case 'Quiz':
          if (
            dataExist.QuizPercent < lesson.PercentCompleted &&
            dataExist.QuizPercent < 100
          ) {
            dataExistUpdate = {
              ...dataExist,
              QuizPercent: lesson?.PercentCompleted ?? 0,
              QuizStep: lesson?.stepOrder + '',
            };
            const response = await courseDA.updateProccessLesson(
              dataExistUpdate,
            );
            if (response) {
              dispatch(
                handleActions({
                  type: UPDATE_PROCESS,
                  lessons: lesson,
                }),
              );
            }
          }
          break;
        case 'Exam':
          if (
            dataExist.ExamPercent < lesson.PercentCompleted &&
            dataExist.ExamPercent < 100
          ) {
            dataExistUpdate = {
              ...dataExist,
              ExamPercent: lesson?.PercentCompleted ?? 0,
              ExamStep: lesson?.stepOrder + '',
            };
            const response = await courseDA.updateProccessLesson(
              dataExistUpdate,
            );
            if (response) {
              dispatch(
                handleActions({
                  type: UPDATE_PROCESS,
                  lessons: lesson,
                }),
              );
            }
          }

          break;
        case 'Video':
          if (!dataExist.DocumentStep?.includes(lesson.Name)) {
            var step = dataExist.DocumentStep ? dataExist.DocumentStep?.split(',') : [];
            step.push(lesson.Name);
            dataExistUpdate = {
              ...dataExist,
              DocumentStep: step.join(','),
              DocumentPercent: lesson?.PercentCompleted ?? 0,
            };
            const response = await courseDA.updateProccessLesson(
              dataExistUpdate,
            );
            debugger
            if (response) {

              dispatch(
                handleActions({
                  type: UPDATE_PROCESS,
                  lessons: lesson,
                }),
              );
            }
          }
          break;
        default:
          break;
      }
    } else {
      var data = {
        Id: randomGID(),
        DateCreated: new Date().getTime(),
        CourseId: lesson.CourseId,
        LessonId: lesson.Id,
        CustomerId: cusId,
      };
      var dataUpdate: any;
      switch (lesson.Type) {
        case 'Intro':
          dataUpdate = {
            ...data,
            IntrolPercent: lesson?.PercentCompleted ?? 0,
            IntrolStep: lesson?.stepOrder + '',
            DocumentStep: '',
            DocumentPercent: 0,
            QuizPercent: 0,
            QuizStep: '',
            ExamStep: '',
            ExamPercent: 0,
          };
          break;
        case 'Video':
          dataUpdate = {
            ...data,
            IntrolPercent: 0,
            IntrolStep: '',
            DocumentStep: lesson?.stepOrder + '',
            DocumentPercent: lesson?.PercentCompleted,
            QuizPercent: 0,
            QuizStep: '',
            ExamPercent: 0,
            ExamStep: '',
          };
          break;
        case 'Quiz':
          dataUpdate = {
            ...data,
            QuizPercent: lesson?.PercentCompleted ?? 0,
            QuizStep: lesson?.stepOrder + '',
            IntrolPercent: 0,
            IntrolStep: '',
            DocumentStep: '',
            DocumentPercent: 0,
            ExamPercent: 0,
            ExamStep: '',
          };
          break;
        case 'Exam':
          dataUpdate = {
            ...data,
            ExamPercent: lesson?.PercentCompleted ?? 0,
            ExamStep: lesson?.stepOrder + '',
            IntrolPercent: 0,
            IntrolStep: '',
            DocumentStep: '',
            DocumentPercent: 0,
            QuizPercent: 0,
            QuizStep: '',
          };
          break;
        default:
          break;
      }
      const response = await courseDA.addProccessLesson(dataUpdate);
      debugger;

      if (response) {
        dispatch(
          handleActions({
            type: UPDATE_PROCESS,
            lessons: lesson,
          }),
        );
      }
    }
  };
  static setCurrentStep = (lesson: any) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: SET_CURRENTSTEP,
        lessons: lesson,
      }),
    );
  };
}
